<template>
  <ellipse
    class="node-text-rect"
    :rx="data.width / 2"
    :ry="data.height / 2"
    :cx="data.width / 2"
    :cy="data.height / 2"
    :style="data.style"
  ></ellipse>

  <Text :data="data" />
</template>

<script setup lang="ts">
import { onMounted } from "vue";
import * as d3 from "d3";

import type { INode } from "@/types";
import { bindNodeDrag } from "@/utils/editor/event";

import Text from "./Text.vue";

const props = defineProps<{
  data: INode;
}>();

onMounted(() => {
  const node = d3.select<SVGGElement, INode>(`#node_${props.data.nodeId}`).data([props.data]);

  bindNodeDrag(node);
});
</script>

<style></style>

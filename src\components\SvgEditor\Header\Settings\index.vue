<template>
  <n-drawer v-model:show="show" :width="350" :show-mask="false" class="bg-#48484e">
    <n-drawer-content title="配置" closable>
      <n-switch v-model:value="configStore.isPlanDividerVisible" size="small">
        <template #checked> 显示 </template>
        <template #unchecked> 隐藏 </template>
      </n-switch>
      <n-switch v-model:value="mapStore.isLinkArrowVisible" size="small">
        <template #checked> 显示 </template>
        <template #unchecked> 隐藏 </template>
      </n-switch>
    </n-drawer-content>
  </n-drawer>

  <n-button quaternary size="small" class="mr-1" :disabled="!mapStore.mapInfo" @click="show = true">
    <n-icon><Settings /></n-icon>
  </n-button>

  <!-- <n-popover placement="bottom" trigger="click">
    <template #trigger>
      <n-button quaternary size="small" class="mr-1" :disabled="!mapStore.mapInfo">
        <template #icon>
          <n-icon><Settings /></n-icon>
        </template>
      </n-button>
    </template>
    <n-scrollbar class="w-80 h-80vh">
      <n-collapse>
        <n-collapse-item title="检查">
          <n-button size="small" class="mr-1"> 关系 </n-button>
          <n-button size="small" class="mr-1"> 模型 </n-button>
          <n-button size="small" class="mr-1"> 节点 </n-button>
          <n-button size="small" class="mr-1"> 连接关系 </n-button>
        </n-collapse-item>
      </n-collapse>
    </n-scrollbar>
  </n-popover> -->
</template>

<script setup lang="ts">
import { ref } from "vue";
import { Settings } from "@/utils/components/icons";
import { useMapStore, useConfigStore } from "@/stores";

const mapStore = useMapStore();
const configStore = useConfigStore();

const show = ref(false);
</script>

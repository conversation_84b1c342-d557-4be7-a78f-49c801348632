<template>
  <BaseHeader>
    <template #left>
      <n-dropdown size="small" @select="handleSelect">
        <n-button quaternary size="small" class="mr-1"> 编辑 </n-button>
      </n-dropdown>
      <n-button quaternary size="small" class="mr-1" @click="toTargetPage('preview')">
        查看
      </n-button>

      <n-dropdown size="small" :options="exportOptions" @select="handleSelect">
        <n-button quaternary size="small"> 导出 </n-button>
      </n-dropdown>
    </template>

    <template #right>
      <Settings></Settings>
    </template>
  </BaseHeader>
</template>

<script setup lang="ts">
import { useRouter } from "vue-router";

import BaseHeader from "@/components/Common/BaseHeader/index.vue";
import Settings from "./Settings/index.vue";
import { useExportSvg } from "@/hooks/svg/useExportSvg";
import { useDataStore, useMapStore, useMenuStore } from "@/stores";

const dataStore = useDataStore();
const exportSvg = useExportSvg();
const mapStore = useMapStore();
const menuStore = useMenuStore();

const router = useRouter();

const exportOptions = [
  {
    label: "SVG",
    key: "svg"
  },
  {
    label: "PNG",
    key: "png"
  },
  {
    label: "PDF",
    key: "PDF"
  }
];

const handleSelect = (key: string | number) => {
  const date = new Date(),
    obj = {
      year: date.getFullYear(),
      month: date.getMonth() + 1,
      strDate: date.getDate()
    };
  let fileName = obj.year.toString() + obj.month.toString() + obj.strDate.toString();
  if (menuStore.currentMenu && "mapName" in menuStore.currentMenu) {
    fileName = menuStore.currentMenu.mapName;
  }

  switch (key) {
    case "svg":
      if (!dataStore.nodes.length && !dataStore.links.length) return;
      exportSvg.exportSvg(fileName, mapStore.mapSize.width, mapStore.mapSize.height);
      break;
    case "png":
      exportSvg.covertSVG2Image(fileName, mapStore.mapSize.width, mapStore.mapSize.height);
      break;
    case "PDF":
      console.log(33, "PDF");
      break;
  }
};

const toTargetPage = (path: string) => {
  router.push(`/${path}`);
};
</script>

<style scoped></style>

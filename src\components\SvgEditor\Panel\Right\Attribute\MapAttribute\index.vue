<template>
  <BaseItem title="ID">
    <p class="truncate" :title="mapStore.mapInfo?.mapId">
      {{ mapStore.mapInfo?.mapId }}
    </p>
  </BaseItem>
  <BaseItem title="尺寸">
    <n-flex :wrap="false">
      <n-input-number
        v-model:value="mapStore.mapSize.width"
        size="small"
        :show-button="false"
        @update:value="onSizeChange"
      />
      <n-input-number
        v-model:value="mapStore.mapSize.height"
        size="small"
        :show-button="false"
        @update:value="onSizeChange"
      />
    </n-flex>
  </BaseItem>

  <n-collapse :default-expanded-names="['background']">
    <n-collapse-item title="背景" name="background">
      <MapBackground></MapBackground>
    </n-collapse-item>
    <n-collapse-item title="缩放" name="zoom">
      <MapZoom></MapZoom>
    </n-collapse-item>

    <n-collapse-item title="连线" name="link">
      <LinkItem></LinkItem>
    </n-collapse-item>
  </n-collapse>
</template>

<script setup lang="ts">
import { useMapStore } from "@/stores";
import { resetSvgSizePosition } from "@/utils/editor/event";
import { updateMap } from "@/utils/http/apis";

import BaseItem from "../Item/index.vue";

import LinkItem from "./Items/LinkItem.vue";
import MapBackground from "./Items/MapBackground.vue";
import MapZoom from "./Items/MapZoom.vue";

const mapStore = useMapStore();

const onSizeChange = async () => {
  const { width, height } = mapStore.mapSize;
  if (!mapStore.mapInfo) return;
  mapStore.mapInfo.width = width;
  mapStore.mapInfo.height = height;
  mapStore.mapInfo.mapSize = `${width}*${height}`;

  await updateMap(mapStore.mapInfo!);
  window.$message.success("更新成功");
  resetSvgSizePosition();
};
</script>

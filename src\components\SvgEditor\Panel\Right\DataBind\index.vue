<template>
  <DataBindItem
    v-for="item in dataStore.groupSelected"
    :key="item.groupId"
    :data="item"
  ></DataBindItem>
  <BaseItem title="ID" v-if="!dataStore.groupSelected.length">
    <!-- <n-input placeholder="唯一ID" size="small" @update:value="onAttributeChange($event, 'domId')" /> -->
    <n-input
      :value="dataStore.currentNode?.domId || dataStore.currentLink?.domId || null"
      placeholder="唯一ID"
      size="small"
      @update:value="onAttributeChange($event, 'domId')"
    />
  </BaseItem>
</template>

<script setup lang="ts">
import { ref } from "vue";

import BaseItem from "@/components/SvgEditor/Panel/Right/Attribute/Item/index.vue";
import DataBindItem from "@/components/SvgEditor/Panel/Right/DataBind/DataBindList.vue";
import { useDataStore, useMapStore } from "@/stores";
import type { IGroupData, ILink, IMouseEvent, INode } from "@/types";
import { updateLink, updateMapGroupData, updateNode, updateNodesLinks } from "@/utils/http/apis";
import { getGroupDataList } from "@/utils/tools";

const dataStore = useDataStore();
const mapStore = useMapStore();
const currentGroup = ref<IGroupData>();

// 更新节点属性
const updateGroupAttribute = async () => {
  if (!currentGroup.value) return;
  const { nodes, links } = currentGroup.value;
  const mapId = mapStore.mapInfo!.mapId;

  await updateMapGroupData({
    ...currentGroup.value,
    mapId,
    topoMapsGroupDataList: getGroupDataList(nodes, links)
  });

  window.$message.success("更新成功");
};

// 更新节点属性
const updateNodeAttributeByHttp = () => {
  if (!dataStore.currentNode) return;
  updateNode([dataStore.currentNode]);
};

const updateLinkAttributeByHttp = () => {
  if (!dataStore.currentLink) return;
  updateLink([dataStore.currentLink]);
};

const onAttributeChange = (value: string, key: "domId") => {
  if (dataStore.currentNode) {
    dataStore.currentNode[key] = value;
    updateNodeAttributeByHttp();
  } else if (dataStore.currentLink) {
    dataStore.currentLink[key] = value;
    updateLinkAttributeByHttp();
  }
};

const onSyncId = () => {
  const defaultMouseEvent: IMouseEvent = {
    eventType: "click",
    groupName: "",
    height: 800,
    name: "",
    resourceCode: "",
    resourceId: "",
    value: "",
    width: 1000
  };

  const syncElementId = (elements: INode[] | ILink[]) => {
    elements.forEach((element) => {
      const mouseEvent = element.mouseEvent?.length ? element.mouseEvent : [defaultMouseEvent];

      element.mouseEvent = mouseEvent.map((item) => ({
        ...item,
        value: element.domId
      }));
    });
  };

  syncElementId(dataStore.nodesSelected);
  syncElementId(dataStore.linksSelected);
  updateNodesLinks({
    nodes: dataStore.nodesSelected,
    links: dataStore.linksSelected
  });
};
</script>

<style></style>

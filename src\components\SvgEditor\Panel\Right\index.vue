<template>
  <div class="attribute-panel absolute top-5 right-0 w-80 h-90% px-2" :style="translate">
    <n-tabs v-model:value="tabActiveName" type="line" animated>
      <n-tab-pane name="attribute" tab="属性">
        <Attribute></Attribute>
      </n-tab-pane>
      <!-- <n-tab-pane name="model" tab="模型"> 模型 </n-tab-pane> -->
      <n-tab-pane name="layer" tab="子图层" :disabled="!mapStore.mapInfo" display-directive="show">
        <SublayerList
      /></n-tab-pane>
      <n-tab-pane name="data" tab="数据" display-directive="show"> <DataBind /> </n-tab-pane>

      <n-tab-pane name="event" tab="事件">
        <EventBind />
      </n-tab-pane>
      <n-tab-pane name="script" tab="脚本">
        <Script></Script>
      </n-tab-pane>
    </n-tabs>

    <n-button
      circle
      size="small"
      class="absolute -right-4 -top-4"
      color="#d5d5d5"
      @click="closeAttributeView"
    >
      <template #icon>
        <n-icon><Close /></n-icon>
      </template>
    </n-button>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from "vue";

import { useCommonStore, useMapStore } from "@/stores/";
import { Close } from "@/utils/components/icons";

import Attribute from "./Attribute/index.vue";
import DataBind from "./DataBind/index.vue";
import EventBind from "./EventBind/index.vue";
import Script from "./Script/index.vue";
import SublayerList from "./SublayerList/index.vue";

const commonStore = useCommonStore();
const mapStore = useMapStore();

const tabActiveName = ref("attribute");

const translate = computed(() => {
  return {
    transform:
      commonStore.isAttributeViewVisible && mapStore.mapInfo
        ? "translateX(-20px)"
        : "translateX(100%)"
  };
});

const closeAttributeView = () => {
  commonStore.isAttributeViewVisible = false;
};

watch(
  () => mapStore.mapInfo?.mapId,
  () => {
    tabActiveName.value = "attribute";
  }
);
</script>

<style scoped>
.attribute-panel {
  transition: all 0.3s;
  background-color: #48484e;
}
</style>
